<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Google Places API 測試 - Restaurant Roulette</title>
    <style>
        :root {
            --primary-color: #ff6b35;
            --secondary-color: #f7931e;
            --accent-color: #ffd23f;
            --background-color: #1a1a2e;
            --surface-color: #16213e;
            --text-primary: #ffffff;
            --text-secondary: #e5e5e5;
            --success-color: #4caf50;
            --warning-color: #ff9800;
            --border-radius: 12px;
            --shadow-lg: 0 10px 25px rgba(255, 107, 53, 0.3);
        }
        
        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: linear-gradient(135deg, var(--background-color) 0%, var(--surface-color) 100%);
            color: var(--text-primary);
            min-height: 100vh;
        }
        
        h1 {
            text-align: center;
            background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 2rem;
        }
        
        .test-result {
            background: var(--surface-color);
            padding: 15px;
            margin: 10px 0;
            border-radius: var(--border-radius);
            border-left: 4px solid var(--primary-color);
            box-shadow: var(--shadow-lg);
        }
        .success { border-left-color: var(--success-color); }
        .error { border-left-color: #f44336; }
        .loading { border-left-color: var(--warning-color); }
        
        button {
            background: var(--primary-color);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: var(--border-radius);
            cursor: pointer;
            margin: 10px 5px;
            font-weight: 600;
            transition: all 0.3s ease;
            box-shadow: var(--shadow-lg);
        }
        button:hover { 
            background: var(--secondary-color);
            transform: translateY(-2px);
        }
        
        pre {
            background: #000;
            padding: 15px;
            border-radius: var(--border-radius);
            overflow-x: auto;
            font-size: 12px;
            border: 1px solid #333;
        }
        
        .button-group {
            display: flex;
            gap: 10px;
            justify-content: center;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <h1>🍽️ 餐廳輪盤 - Google Places API 測試</h1>
    
    <div id="status" class="test-result loading">
        ⏳ 準備中...請點擊測試按鈕
    </div>
    
    <div class="button-group">
        <button onclick="testAPI()">🎯 測試 API 功能</button>
        <button onclick="clearResults()">🗑️ 清除結果</button>
    </div>
    
    <div id="results"></div>
    
    <script src="utils/locationUtils.js"></script>
    <script>
        let testResults = [];
        
        // 確保 Google Maps API 載入完成的輔助函數
        async function ensureGoogleMapsLoaded() {
            if (window.google && window.google.maps) {
                return true;
            }
            
            // 等待 API 載入
            return new Promise((resolve) => {
                const checkInterval = setInterval(() => {
                    if (window.google && window.google.maps) {
                        clearInterval(checkInterval);
                        resolve(true);
                    }
                }, 100);
                
                // 10秒超時
                setTimeout(() => {
                    clearInterval(checkInterval);
                    resolve(false);
                }, 10000);
            });
        }
        
        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            testResults.push({ timestamp, message, type });
            updateDisplay();
        }
        
        function updateDisplay() {
            const resultsDiv = document.getElementById('results');
            resultsDiv.innerHTML = testResults.map(result => 
                `<div class="test-result ${result.type}">
                    <strong>${result.timestamp}</strong> - ${result.message}
                </div>`
            ).join('');
        }
        
        function clearResults() {
            testResults = [];
            updateDisplay();
            document.getElementById('status').className = 'test-result loading';
            document.getElementById('status').innerHTML = '⏳ 結果已清除...';
        }
        
        async function testAPI() {
            const statusDiv = document.getElementById('status');
            statusDiv.className = 'test-result loading';
            statusDiv.innerHTML = '🔄 測試進行中...';
            
            try {
                log('🚀 開始測試 Google Places API 整合', 'info');
                
                // 測試地理位置
                log('📍 檢查地理位置支援...', 'info');
                if (!navigator.geolocation) {
                    throw new Error('瀏覽器不支援地理位置');
                }
                log('✅ 地理位置 API 可用', 'success');
                
                // 獲取用戶位置
                log('🌍 獲取用戶位置...', 'info');
                const position = await new Promise((resolve, reject) => {
                    navigator.geolocation.getCurrentPosition(resolve, reject, {
                        enableHighAccuracy: true,
                        timeout: 10000,
                        maximumAge: 300000
                    });
                });
                
                const userLocation = {
                    lat: position.coords.latitude,
                    lng: position.coords.longitude
                };
                
                log(`📍 位置獲取成功: ${userLocation.lat.toFixed(4)}, ${userLocation.lng.toFixed(4)}`, 'success');
                
                // 確保 Google Maps API 已載入
                log('🗺️ 檢查 Google Maps API...', 'info');
                const mapsLoaded = await ensureGoogleMapsLoaded();
                if (!mapsLoaded) {
                    throw new Error('Google Maps API 載入超時');
                }
                log('✅ Google Maps API 已準備就緒', 'success');
                
                // 測試餐廳搜索
                log('🔍 測試 Google Places API 餐廳搜索...', 'info');
                const restaurant = await getRandomRestaurant(userLocation);
                
                if (restaurant) {
                    log('🎉 餐廳搜索成功！', 'success');
                    log(`📄 餐廳詳情：<pre>${JSON.stringify(restaurant, null, 2)}</pre>`, 'success');
                    
                    statusDiv.className = 'test-result success';
                    statusDiv.innerHTML = `✅ 測試成功！找到餐廳：${restaurant.name}`;
                } else {
                    throw new Error('餐廳搜索返回空結果');
                }
                
            } catch (error) {
                log(`❌ 測試失敗：${error.message}`, 'error');
                log(`🔧 錯誤詳情：<pre>${error.stack || error}</pre>`, 'error');
                
                statusDiv.className = 'test-result error';
                statusDiv.innerHTML = `❌ 測試失敗：${error.message}`;
            }
        }
        
        // 監聽控制台輸出
        const originalLog = console.log;
        const originalError = console.error;
        
        console.log = function(...args) {
            originalLog.apply(console, args);
            log(`📝 控制台：${args.join(' ')}`, 'info');
        };
        
        console.error = function(...args) {
            originalError.apply(console, args);
            log(`🚨 控制台錯誤：${args.join(' ')}`, 'error');
        };
        
        log('🔧 測試頁面已載入，準備就緒', 'info');
    </script>
</body>
</html>
