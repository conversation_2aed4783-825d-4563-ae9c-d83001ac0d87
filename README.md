# 🍽️ Restaurant Roulette - 餐廳輪盤

一個有趣的餐廳發現應用程式，使用 Google Places API 搜索您附近的真實餐廳！

## ✨ 最新更新

### 🎯 Google Places API 整合 (2025-07-31)

- **真實餐廳搜索**：使用 Google Places API 替換模擬數據
- **智能地點偵測**：自動獲取用戶位置並搜索 5km 範圍內餐廳
- **完整錯誤處理**：提供詳細技術資訊以供偵錯
- **回退機制**：API 失敗時自動切換到模擬數據
- **用戶友好**：保持原有 UI/UX，無縫升級

## 🚀 功能特色

- 🎰 **輪盤式餐廳選擇**：有趣的旋轉動畫
- 📍 **真實地點搜索**：基於 Google Places API
- 🌍 **自動定位**：使用瀏覽器地理位置 API
- 🔄 **智能回退**：確保應用始終可用
- 🌐 **多語言支援**：英文 / 繁體中文
- 📱 **響應式設計**：適配各種裝置

## 🛠️ 技術架構

### API 配置
- **Google Places API 金鑰**：`AIzaSyC7tj6X8c5QwX0uVR7fUNcate1Sapn7lyQ`
- **搜索範圍**：5000 公尺（5公里）
- **語言設定**：`zh-TW`（繁體中文）
- **地區設定**：`TW`（台灣）

### 文件結構
```
restaurant-roulette/
├── index.html              # 主頁面
├── app.js                  # 主應用邏輯
├── config/
│   └── api-config.js      # API 配置（預留）
├── components/
│   ├── LanguageSelector.js # 語言選擇器
│   ├── SlotMachine.js     # 輪盤組件
│   └── RestaurantCard.js  # 餐廳卡片
├── utils/
│   ├── locationUtils.js   # 🆕 Google Places API 整合
│   └── mockData.js        # 模擬數據（回退用）
└── test-api.html          # 🆕 API 測試頁面
```

## 📋 使用說明

1. **開啟應用程式**：在瀏覽器中開啟 `index.html`
2. **允許位置存取**：點擊允許瀏覽器獲取您的位置
3. **轉動輪盤**：點擊「轉動尋找餐廳」按鈕
4. **查看結果**：獲得附近真實餐廳的詳細資訊

## 🔧 測試工具

### API 測試頁面
開啟 `test-api.html` 來測試 Google Places API 功能：
- 檢查地理位置支援
- 測試 API 連接
- 驗證數據格式
- 查看詳細錯誤資訊

### 除錯資訊
當發生錯誤時，應用會顯示：
- 錯誤類型和訊息
- 時間戳記
- 用戶位置資訊
- API 金鑰狀態（已遮罩）

## ⚡ 錯誤處理機制

### 自動回退系統
1. **優先使用 Google Places API** 搜索真實餐廳
2. **失敗時自動切換** 到模擬數據
3. **詳細錯誤記錄** 便於偵錯和回報

### 常見錯誤處理
- **位置獲取失敗**：顯示友好提示，要求用戶允許位置存取
- **API 配額超限**：自動使用模擬數據，不影響用戶體驗
- **網路連接問題**：提供重試機制和技術細節

## 🔐 隱私與安全

- **位置資料**：僅用於搜索附近餐廳，不儲存或傳輸
- **API 金鑰**：已設定域名限制和使用配額
- **用戶資料**：無個人資料收集或儲存

## 🌟 未來規劃

- [ ] 餐廳收藏功能
- [ ] 更多篩選選項（價格、評分、距離）
- [ ] 社群分享功能
- [ ] 餐廳評論整合
- [ ] 離線模式支援

## 🤝 技術支援

如遇到問題，請查看瀏覽器控制台的詳細錯誤訊息，或使用 `test-api.html` 進行診斷。

## 🤖 AI 開發協助規則

- **無論用戶使用何種語言，AI都用繁體中文回應**
- **修改代碼結束後幫我GIT COMMIT並 PUSH --force**
- **最小化成程度修改，不要優化**
---

**Commit Hash**: `9b94b11`  
**最後更新**: 2025-07-31  
**版本**: v2.1 - 介面優化與功能改進版
